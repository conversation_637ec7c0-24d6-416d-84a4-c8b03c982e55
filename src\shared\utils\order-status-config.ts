import { CheckCircle2, Clock, PackageCheck, Truck, XCircle } from "lucide-react";

export const orderStatusConfig: Record<string, { label: string; color: string; icon: React.ElementType }> = {
	Aberto: { label: "Aberto", color: "bg-yellow-100 text-yellow-800", icon: Clock },
	"Em Processamento": { label: "Em Processamento", color: "bg-indigo-100 text-indigo-800", icon: Truck },
	Finalizado: { label: "Finalizado", color: "bg-green-100 text-green-800", icon: CheckCircle2 },
	Cancelado: { label: "Cancelado", color: "bg-red-100 text-red-700", icon: XCircle },
};

export function getOrderStatusConfig(status: string) {
	return orderStatusConfig[status] || { label: status, color: "bg-gray-100 text-gray-500", icon: () => null };
}
